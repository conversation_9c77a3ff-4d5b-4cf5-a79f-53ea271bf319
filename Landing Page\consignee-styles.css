/* Consignee Page Specific Styles */

/* Main Content Layout */
.main-content {
    margin-top: 140px; /* Account for fixed header and menu */
    padding: 2rem;
    min-height: calc(100vh - 140px);
    background: var(--background-primary);
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.page-title-section {
    flex: 1;
}

.page-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.page-title i {
    color: var(--accent-color);
}

.page-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: var(--accent-color);
    color: var(--secondary-color);
}

.btn-primary:hover {
    background: var(--accent-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--shadow-medium);
}

.btn-secondary {
    background: var(--background-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--border-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--shadow-light);
}

/* Filters Section */
.filters-section {
    background: var(--card-background);
    border: 1px solid var(--card-border);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px var(--shadow-light);
}

.search-filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 1.5rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.filter-input,
.filter-select {
    padding: 0.75rem;
    border: 1px solid var(--input-border);
    border-radius: 6px;
    background: var(--input-background);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--input-focus);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

/* Grid Container */
.consignee-grid-container {
    background: var(--card-background);
    border: 1px solid var(--card-border);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px var(--shadow-light);
}

.grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: var(--background-secondary);
    border-bottom: 1px solid var(--card-border);
}

.grid-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.grid-controls {
    display: flex;
    gap: 0.5rem;
}

.view-toggle {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    background: var(--background-primary);
    color: var(--text-secondary);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-toggle:hover,
.view-toggle.active {
    background: var(--accent-color);
    color: var(--secondary-color);
    border-color: var(--accent-color);
}

/* Consignee Grid */
.consignee-grid {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.consignee-grid.list-view {
    grid-template-columns: 1fr;
}

/* Consignee Card */
.consignee-card {
    background: var(--card-background);
    border: 1px solid var(--card-border);
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.consignee-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px var(--shadow-medium);
    border-color: var(--accent-color);
}

.consignee-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.consignee-info h3 {
    margin: 0 0 0.25rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.consignee-code {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-family: monospace;
    background: var(--background-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.consignee-status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-active {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.status-inactive {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.status-pending {
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.consignee-details {
    display: grid;
    gap: 0.75rem;
}

.detail-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.detail-row i {
    width: 16px;
    color: var(--text-secondary);
}

.detail-row span {
    color: var(--text-primary);
}

.consignee-actions {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--card-border);
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    background: var(--background-primary);
    color: var(--text-primary);
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
}

.action-btn:hover {
    background: var(--accent-color);
    color: var(--secondary-color);
    border-color: var(--accent-color);
}

/* List View Styles */
.consignee-grid.list-view .consignee-card {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    align-items: center;
    padding: 1rem 1.5rem;
}

.consignee-grid.list-view .consignee-header {
    margin-bottom: 0;
}

.consignee-grid.list-view .consignee-details {
    display: flex;
    gap: 1rem;
}

.consignee-grid.list-view .consignee-actions {
    margin-top: 0;
    padding-top: 0;
    border-top: none;
    justify-content: flex-end;
}

/* No Results State */
.no-results {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.no-results i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-results h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    color: var(--text-primary);
}

.no-results p {
    margin: 0;
    font-size: 0.9rem;
}

/* Notification Styles */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification {
    animation: slideIn 0.3s ease;
}

/* Loading State */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.loading i {
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }

    .page-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .page-actions {
        justify-content: flex-start;
    }

    .search-filters {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .consignee-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .consignee-grid.list-view .consignee-card {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .consignee-grid.list-view .consignee-details {
        flex-direction: column;
        align-items: flex-start;
    }
}
