// Consignee Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    console.log('Consignee page loaded');
    
    // Initialize parent-child window relationship
    initializeChildWindow();
    
    // Initialize consignee functionality
    initializeConsigneeManagement();
});

// Parent-Child Window Management for Consignee Page
function initializeChildWindow() {
    console.log('Initializing child window management...');
    
    // Check if this window has a parent (opener)
    if (window.opener && !window.opener.closed) {
        console.log('Child window detected, setting up parent relationship');
        
        // Monitor parent window status
        const checkParent = setInterval(function() {
            if (window.opener.closed) {
                console.log('Parent window closed, closing child window');
                clearInterval(checkParent);
                window.close();
            }
        }, 1000);
        
        // Handle child window closing
        window.addEventListener('beforeunload', function() {
            console.log('Child window closing');
        });
        
        // Set window title to indicate it's a child window
        document.title = 'HCLSoftware - Consignee Management (Child Window)';
        
    } else {
        console.log('Standalone window detected');
        document.title = 'HCLSoftware - Consignee Management';
    }
}

// Sample consignee data
const sampleConsignees = [
    {
        id: 'CSG001',
        name: 'ABC Manufacturing Ltd.',
        code: 'ABC-MFG-001',
        location: 'North Region',
        address: '123 Industrial Ave, Manufacturing District',
        contact: '******-0123',
        email: '<EMAIL>',
        status: 'active',
        lastUpdated: '2024-01-15'
    },
    {
        id: 'CSG002',
        name: 'XYZ Logistics Corp.',
        code: 'XYZ-LOG-002',
        location: 'South Region',
        address: '456 Logistics Blvd, Transport Hub',
        contact: '******-0456',
        email: '<EMAIL>',
        status: 'active',
        lastUpdated: '2024-01-14'
    },
    {
        id: 'CSG003',
        name: 'Global Trade Solutions',
        code: 'GTS-003',
        location: 'East Region',
        address: '789 Trade Center, Business Park',
        contact: '******-0789',
        email: '<EMAIL>',
        status: 'pending',
        lastUpdated: '2024-01-13'
    },
    {
        id: 'CSG004',
        name: 'Metro Distribution Inc.',
        code: 'MDI-004',
        location: 'West Region',
        address: '321 Distribution Way, Metro Area',
        contact: '******-0321',
        email: '<EMAIL>',
        status: 'inactive',
        lastUpdated: '2024-01-12'
    },
    {
        id: 'CSG005',
        name: 'Coastal Shipping Co.',
        code: 'CSC-005',
        location: 'South Region',
        address: '654 Harbor Drive, Port District',
        contact: '******-0654',
        email: '<EMAIL>',
        status: 'active',
        lastUpdated: '2024-01-11'
    },
    {
        id: 'CSG006',
        name: 'Mountain Freight Services',
        code: 'MFS-006',
        location: 'North Region',
        address: '987 Mountain Pass, Highland Area',
        contact: '******-0987',
        email: '<EMAIL>',
        status: 'active',
        lastUpdated: '2024-01-10'
    }
];

let filteredConsignees = [...sampleConsignees];
let currentView = 'grid';

function initializeConsigneeManagement() {
    console.log('Initializing consignee management...');
    
    // Get DOM elements
    const consigneeGrid = document.getElementById('consignee-grid');
    const searchInput = document.getElementById('consignee-search');
    const statusFilter = document.getElementById('status-filter');
    const locationFilter = document.getElementById('location-filter');
    const viewToggles = document.querySelectorAll('.view-toggle');
    const addBtn = document.getElementById('add-consignee-btn');
    const exportBtn = document.getElementById('export-consignees-btn');
    const refreshBtn = document.getElementById('refresh-consignees-btn');
    
    // Initialize view
    renderConsignees();
    
    // Event listeners
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }
    
    if (statusFilter) {
        statusFilter.addEventListener('change', handleFilter);
    }
    
    if (locationFilter) {
        locationFilter.addEventListener('change', handleFilter);
    }
    
    viewToggles.forEach(toggle => {
        toggle.addEventListener('click', handleViewToggle);
    });
    
    if (addBtn) {
        addBtn.addEventListener('click', handleAddConsignee);
    }
    
    if (exportBtn) {
        exportBtn.addEventListener('click', handleExport);
    }
    
    if (refreshBtn) {
        refreshBtn.addEventListener('click', handleRefresh);
    }
    
    console.log('Consignee management initialized');
}

function renderConsignees() {
    const consigneeGrid = document.getElementById('consignee-grid');
    if (!consigneeGrid) return;
    
    // Update grid class based on current view
    consigneeGrid.className = `consignee-grid ${currentView === 'list' ? 'list-view' : ''}`;
    
    if (filteredConsignees.length === 0) {
        consigneeGrid.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h3>No consignees found</h3>
                <p>Try adjusting your search criteria or filters.</p>
            </div>
        `;
        return;
    }
    
    consigneeGrid.innerHTML = filteredConsignees.map(consignee => `
        <div class="consignee-card" data-id="${consignee.id}">
            <div class="consignee-header">
                <div class="consignee-info">
                    <h3>${consignee.name}</h3>
                    <div class="consignee-code">${consignee.code}</div>
                </div>
                <div class="consignee-status status-${consignee.status}">
                    ${consignee.status}
                </div>
            </div>
            <div class="consignee-details">
                <div class="detail-row">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>${consignee.location}</span>
                </div>
                <div class="detail-row">
                    <i class="fas fa-home"></i>
                    <span>${consignee.address}</span>
                </div>
                <div class="detail-row">
                    <i class="fas fa-phone"></i>
                    <span>${consignee.contact}</span>
                </div>
                <div class="detail-row">
                    <i class="fas fa-envelope"></i>
                    <span>${consignee.email}</span>
                </div>
                <div class="detail-row">
                    <i class="fas fa-calendar"></i>
                    <span>Updated: ${consignee.lastUpdated}</span>
                </div>
            </div>
            <div class="consignee-actions">
                <button class="action-btn" onclick="viewConsignee('${consignee.id}')">
                    <i class="fas fa-eye"></i> View
                </button>
                <button class="action-btn" onclick="editConsignee('${consignee.id}')">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="action-btn" onclick="deleteConsignee('${consignee.id}')">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        </div>
    `).join('');
}

function handleSearch() {
    const searchTerm = document.getElementById('consignee-search').value.toLowerCase();
    applyFilters();
}

function handleFilter() {
    applyFilters();
}

function applyFilters() {
    const searchTerm = document.getElementById('consignee-search').value.toLowerCase();
    const statusFilter = document.getElementById('status-filter').value;
    const locationFilter = document.getElementById('location-filter').value;
    
    filteredConsignees = sampleConsignees.filter(consignee => {
        const matchesSearch = !searchTerm || 
            consignee.name.toLowerCase().includes(searchTerm) ||
            consignee.code.toLowerCase().includes(searchTerm) ||
            consignee.location.toLowerCase().includes(searchTerm) ||
            consignee.address.toLowerCase().includes(searchTerm);
            
        const matchesStatus = !statusFilter || consignee.status === statusFilter;
        const matchesLocation = !locationFilter || consignee.location.toLowerCase().includes(locationFilter);
        
        return matchesSearch && matchesStatus && matchesLocation;
    });
    
    renderConsignees();
}

function handleViewToggle(event) {
    const viewType = event.currentTarget.getAttribute('data-view');
    currentView = viewType;
    
    // Update toggle buttons
    document.querySelectorAll('.view-toggle').forEach(toggle => {
        toggle.classList.remove('active');
    });
    event.currentTarget.classList.add('active');
    
    renderConsignees();
}

function handleAddConsignee() {
    alert('Add Consignee functionality would open a form modal here.');
}

function handleExport() {
    alert('Export functionality would generate a CSV/Excel file here.');
}

function handleRefresh() {
    // Reset filters
    document.getElementById('consignee-search').value = '';
    document.getElementById('status-filter').value = '';
    document.getElementById('location-filter').value = '';
    
    // Reset data
    filteredConsignees = [...sampleConsignees];
    renderConsignees();
    
    // Show notification
    showNotification('Consignee list refreshed', 'success');
}

// Consignee action functions
function viewConsignee(id) {
    const consignee = sampleConsignees.find(c => c.id === id);
    if (consignee) {
        alert(`Viewing consignee: ${consignee.name}\nCode: ${consignee.code}\nStatus: ${consignee.status}`);
    }
}

function editConsignee(id) {
    const consignee = sampleConsignees.find(c => c.id === id);
    if (consignee) {
        alert(`Edit consignee functionality would open an edit form for: ${consignee.name}`);
    }
}

function deleteConsignee(id) {
    const consignee = sampleConsignees.find(c => c.id === id);
    if (consignee && confirm(`Are you sure you want to delete consignee: ${consignee.name}?`)) {
        alert(`Delete functionality would remove: ${consignee.name}`);
    }
}

// Notification function (simplified version)
function showNotification(message, type = 'info') {
    console.log(`${type.toUpperCase()}: ${message}`);
    
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
