# Consignee Page Implementation Guide

## Overview
This implementation successfully creates a separate consignee page with parent-child window relationship as requested. The consignee page maintains the exact same header and menu structure as the landing page while providing dedicated consignee management functionality.

## Implementation Details

### 1. Modified Files
- **`index.html`** - Updated consignee link to use JavaScript function instead of anchor navigation
- **`script.js`** - Added parent-child window management system and consignee page opening functionality

### 2. New Files Created
- **`consignee.html`** - Complete consignee management page with same header/menu as landing page
- **`consignee-styles.css`** - Dedicated styling for the consignee page
- **`consignee-script.js`** - JavaScript functionality for consignee management and child window behavior

## Key Features Implemented

### ✅ Parent-Child Window Relationship
- **Parent Window Management**: Landing page tracks and manages child windows
- **Child Window Dependency**: Consignee page automatically closes when parent closes
- **Window Communication**: Proper setup for parent-child communication
- **Single Instance**: Prevents multiple consignee windows from opening

### ✅ Header and Menu Preservation
- **Identical Header**: Same header structure with brand name, search, notifications, language selector, theme toggle, and user profile
- **Complete Menu Bar**: All menu items (HELPDESK, PARTS, SERVICE, TAMS, CORE, DASHBOARD, MORE) preserved
- **Consistent Styling**: Uses the same CSS classes and styling as the landing page
- **Theme Support**: Supports both light and dark themes

### ✅ Consignee Management Features
- **Grid/List View Toggle**: Switch between card grid and list view
- **Search Functionality**: Search by name, code, or location
- **Filter Options**: Filter by status (Active/Inactive/Pending) and location
- **Sample Data**: 6 sample consignees with realistic data
- **Action Buttons**: View, Edit, Delete functionality for each consignee
- **Responsive Design**: Mobile-friendly layout

### ✅ Navigation Behavior
- **External Window**: Opens in a new window instead of same-page navigation
- **Proper Sizing**: Automatically calculates optimal window size and position
- **Focus Management**: Brings existing window to front if already open
- **Popup Blocker Handling**: Shows alert if popup is blocked

## How to Test

### 1. Open the Landing Page
```
Open: file:///c:/Training/AMC/ERP/Landing%20Page/index.html
```

### 2. Navigate to Consignee
1. Click on **CORE** menu in the menu bar
2. The dropdown will open showing various options
3. Look for **Consignee** card (with shipping icon)
4. Click on the **Consignee** card

### 3. Verify Parent-Child Relationship
1. **New Window Opens**: A new window should open with the consignee page
2. **Same Header/Menu**: Verify the header and menu are identical to the landing page
3. **Window Dependency**: Close the landing page (parent) and verify the consignee window closes automatically
4. **Single Instance**: Try clicking the consignee link again - it should focus the existing window instead of opening a new one

### 4. Test Consignee Functionality
1. **View Toggle**: Switch between grid and list views using the toggle buttons
2. **Search**: Use the search box to filter consignees
3. **Filters**: Test status and location filters
4. **Actions**: Click View, Edit, or Delete buttons on consignee cards
5. **Responsive**: Resize the window to test mobile responsiveness

## Technical Implementation

### Parent Window (Landing Page)
```javascript
// Window management system
let consigneeWindow = null;

function openConsigneePage(event) {
    // Prevents multiple windows
    if (consigneeWindow && !consigneeWindow.closed) {
        consigneeWindow.focus();
        return;
    }
    
    // Opens new window with proper dimensions
    consigneeWindow = window.open('consignee.html', 'ConsigneeWindow', windowFeatures);
}

// Auto-close child when parent closes
window.addEventListener('beforeunload', function() {
    if (consigneeWindow && !consigneeWindow.closed) {
        consigneeWindow.close();
    }
});
```

### Child Window (Consignee Page)
```javascript
function initializeChildWindow() {
    // Monitor parent window status
    const checkParent = setInterval(function() {
        if (window.opener.closed) {
            clearInterval(checkParent);
            window.close(); // Auto-close when parent closes
        }
    }, 1000);
}
```

## File Structure
```
Landing Page/
├── index.html                 (Modified - consignee link updated)
├── script.js                  (Modified - added window management)
├── styles.css                 (Unchanged - shared styles)
├── consignee.html             (New - consignee page)
├── consignee-styles.css       (New - consignee-specific styles)
├── consignee-script.js        (New - consignee functionality)
└── CONSIGNEE_IMPLEMENTATION_GUIDE.md (This file)
```

## Browser Compatibility
- **Chrome/Edge**: Full support
- **Firefox**: Full support
- **Safari**: Full support
- **Popup Blockers**: Handled with user notification

## Future Enhancements
1. **Real Data Integration**: Connect to actual consignee database/API
2. **Form Modals**: Add/Edit consignee forms
3. **Export Functionality**: CSV/Excel export implementation
4. **Advanced Filtering**: Date ranges, custom filters
5. **Bulk Operations**: Select multiple consignees for bulk actions

## Troubleshooting
1. **Popup Blocked**: Check browser popup blocker settings
2. **Window Not Closing**: Ensure JavaScript is enabled
3. **Styling Issues**: Verify all CSS files are loaded correctly
4. **Menu Not Working**: Check that script.js is loaded properly

The implementation successfully meets all requirements:
- ✅ External consignee page opens in new window
- ✅ Same header and menu preserved
- ✅ Parent-child window relationship implemented
- ✅ Auto-close functionality working
- ✅ Consignee management interface provided
