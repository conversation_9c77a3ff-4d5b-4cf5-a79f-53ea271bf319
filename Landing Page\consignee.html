<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HCLSoftware - Consignee Management</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="consignee-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Header Section - Same as Landing Page -->
    <header class="main-header">
        <div class="header-content">
            <!-- Left: Brand Name -->
            <div class="header-left">
                <h1 class="header-title">
                    <span class="brand-name" data-i18n="brand-name">HCLSoftware</span>
                </h1>
            </div>

            <!-- Center: Search Bar -->
            <div class="header-center">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" id="search-input" class="search-input" data-i18n-placeholder="search-placeholder" placeholder="Search all menus, items, and bookmarks... (Ctrl+K)" />
                </div>
            </div>

            <!-- Right: Controls -->
            <div class="header-right">
                <!-- Notifications -->
                <div class="notification-dropdown">
                    <button class="notification-toggle" id="notification-toggle" aria-label="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <div class="notification-menu" id="notification-menu">
                        <div class="notification-header">
                            <h3>Notifications</h3>
                            <button class="mark-all-read" id="mark-all-read">Mark all as read</button>
                        </div>
                        <div class="notification-list">
                            <div class="notification-item unread">
                                <div class="notification-icon">
                                    <i class="fas fa-ticket-alt"></i>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-title">New Ticket Created</div>
                                    <div class="notification-message">Ticket #12345 has been created by John Doe</div>
                                    <div class="notification-time">2 minutes ago</div>
                                </div>
                            </div>
                        </div>
                        <div class="notification-footer">
                            <a href="#all-notifications" class="view-all-notifications">View All Notifications</a>
                        </div>
                    </div>
                </div>

                <!-- Language Selector -->
                <div class="language-selector">
                    <button class="language-toggle" id="language-toggle" aria-label="Select language">
                        <i class="fas fa-globe"></i>
                        <span id="current-language">EN</span>
                        <i class="fas fa-chevron-down language-arrow"></i>
                    </button>
                    <div class="language-menu" id="language-menu">
                        <div class="language-option" data-lang="en">
                            <span class="flag">🇺🇸</span>
                            <span>English</span>
                        </div>
                        <div class="language-option" data-lang="es">
                            <span class="flag">🇪🇸</span>
                            <span>Español</span>
                        </div>
                        <div class="language-option" data-lang="fr">
                            <span class="flag">🇫🇷</span>
                            <span>Français</span>
                        </div>
                    </div>
                </div>

                <!-- Theme Toggle -->
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                    <i class="fas fa-sun" id="theme-icon"></i>
                </button>

                <!-- User Profile -->
                <div class="user-profile dropdown">
                    <button class="profile-toggle" id="profile-toggle">
                        <img src="https://i.pravatar.cc/40?img=1" alt="Admin" class="profile-avatar">
                        <span class="profile-name">Admin</span>
                        <i class="fas fa-chevron-down profile-arrow"></i>
                    </button>
                    <div class="dropdown-menu profile-menu" id="profile-menu">
                        <a href="#profile" class="dropdown-item">
                            <i class="fas fa-user"></i>
                            <span data-i18n="profile">Profile</span>
                        </a>
                        <a href="#settings" class="dropdown-item">
                            <i class="fas fa-cog"></i>
                            <span data-i18n="settings">Settings</span>
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#logout" class="dropdown-item logout">
                            <i class="fas fa-sign-out-alt"></i>
                            <span data-i18n="logout">Logout</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Menu Bar - Same as Landing Page -->
    <nav class="menu-bar">
        <div class="menu-container">
            <!-- Main Menu Items -->
            <div class="menu-items-wrapper">
                <div class="menu-item dropdown">
                    <button class="menu-button" data-dropdown="helpdesk">
                        <span data-i18n="helpdesk">HELPDESK</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                </div>

                <div class="menu-item dropdown">
                    <button class="menu-button" data-dropdown="parts">
                        <span data-i18n="parts">PARTS</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                </div>

                <div class="menu-item dropdown">
                    <button class="menu-button" data-dropdown="service">
                        <span data-i18n="service">SERVICE</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                </div>

                <div class="menu-item dropdown">
                    <button class="menu-button" data-dropdown="tams">
                        <span data-i18n="tams">TAMS</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                </div>

                <div class="menu-item dropdown">
                    <button class="menu-button" data-dropdown="core">
                        <span data-i18n="core">CORE</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                </div>

                <div class="menu-item dropdown">
                    <button class="menu-button" data-dropdown="dashboard">
                        <span data-i18n="dashboard">DASHBOARD</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                </div>

                <div class="menu-item dropdown">
                    <button class="menu-button" data-dropdown="more">
                        <span data-i18n="more">MORE</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                </div>
            </div>

            <!-- Menu Icon Buttons -->
            <div class="menu-icon-buttons">
                <button class="icon-button" id="sidebar-toggle-left" title="Bookmarks">
                    <i class="fas fa-bookmark"></i>
                </button>
                <button class="icon-button" id="sidebar-toggle-right" title="Quick Access">
                    <i class="fas fa-star"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content Area -->
    <main class="main-content" id="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="page-title-section">
                <h1 class="page-title">
                    <i class="fas fa-shipping-fast"></i>
                    Consignee Management
                </h1>
                <p class="page-subtitle">Manage and view all consignees in the system</p>
            </div>
            <div class="page-actions">
                <button class="btn btn-primary" id="add-consignee-btn">
                    <i class="fas fa-plus"></i>
                    Add Consignee
                </button>
                <button class="btn btn-secondary" id="export-consignees-btn">
                    <i class="fas fa-download"></i>
                    Export
                </button>
                <button class="btn btn-secondary" id="refresh-consignees-btn">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="filters-section">
            <div class="search-filters">
                <div class="filter-group">
                    <label for="consignee-search">Search Consignees:</label>
                    <input type="text" id="consignee-search" placeholder="Search by name, code, or location..." class="filter-input">
                </div>
                <div class="filter-group">
                    <label for="status-filter">Status:</label>
                    <select id="status-filter" class="filter-select">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="pending">Pending</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="location-filter">Location:</label>
                    <select id="location-filter" class="filter-select">
                        <option value="">All Locations</option>
                        <option value="north">North Region</option>
                        <option value="south">South Region</option>
                        <option value="east">East Region</option>
                        <option value="west">West Region</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Consignee Grid -->
        <div class="consignee-grid-container">
            <div class="grid-header">
                <h2>Consignees List</h2>
                <div class="grid-controls">
                    <button class="view-toggle active" data-view="grid" title="Grid View">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-toggle" data-view="list" title="List View">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
            
            <div class="consignee-grid" id="consignee-grid">
                <!-- Consignee items will be populated here by JavaScript -->
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="script.js"></script>
    <script src="consignee-script.js"></script>
</body>
</html>
