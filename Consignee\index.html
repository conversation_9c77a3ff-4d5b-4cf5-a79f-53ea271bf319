<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Consignee Management - B&W Enhanced</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="min-h-screen p-4 sm:p-6 md:p-8">

    <div class="container mx-auto max-w-7xl">
        <header class="mb-8 p-6 bg-neutral-800 rounded-xl shadow-2xl"> 
            <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                <h1 class="text-3xl font-bold text-neutral-100"> Consignee Dashboard</h1>
                <div id="branch-dropdown-container" class="searchable-dropdown-container w-full sm:w-64">
                    <input type="text" id="branch-search-input" placeholder="Search Branch..."
                           class="searchable-dropdown-input bg-neutral-700 border border-neutral-600 text-neutral-200 text-sm rounded-lg focus:ring-neutral-500 focus:border-neutral-500 block w-full p-3">
                    <div id="branch-dropdown-list" class="searchable-dropdown-list hidden">
                        </div>
                    <input type="hidden" id="selected-branch-value"> 
                </div>
            </div>
        </header>

        <div class="mb-6 p-4 bg-neutral-800 rounded-xl shadow-lg">
            <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                <div class="flex flex-wrap gap-3">
                    <button id="add-consignee-btn" class="flex items-center bg-neutral-600 hover:bg-neutral-500 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition-all duration-150 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-neutral-400 focus:ring-opacity-50">
                        <i class="fas fa-plus mr-2"></i> Add New
                    </button>
                    <button id="delete-selected-btn" class="flex items-center bg-red-700 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition-all duration-150 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50">
                        <i class="fas fa-trash-alt mr-2"></i> Delete Selected
                    </button>
                </div>
                <div class="flex flex-wrap gap-3 items-center">
                     <div class="relative">
                        <input type="text" id="search-input" placeholder="Search all fields..." class="bg-neutral-700 border border-neutral-600 text-neutral-200 text-sm rounded-lg focus:ring-neutral-500 focus:border-neutral-500 pl-10 pr-4 py-2 w-full sm:w-auto">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-neutral-400"></i>
                        </div>
                    </div>
                    <button id="export-btn" title="Export Data" class="flex items-center bg-green-700 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition-all duration-150 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50">
                        <i class="fas fa-file-export mr-0 sm:mr-2"></i> <span class="hidden sm:inline">Export</span>
                    </button>
                    <button id="refresh-btn" title="Refresh Data" class="flex items-center bg-neutral-600 hover:bg-neutral-500 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition-all duration-150 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-neutral-400 focus:ring-opacity-50">
                        <i class="fas fa-sync-alt mr-0 sm:mr-2"></i> <span class="hidden sm:inline">Refresh</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="bg-neutral-800 rounded-xl shadow-2xl overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-neutral-700">
                    <thead class="table-header-sticky"> 
                        <tr>
                            <th scope="col" class="p-4"><input type="checkbox" id="select-all-checkbox" class="custom-checkbox"></th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider sortable-header" data-sort-key="location">Location <span class="sort-indicator"></span></th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider sortable-header" data-sort-key="type">Type <span class="sort-indicator"></span></th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider sortable-header" data-sort-key="address">Address <span class="sort-indicator"></span></th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider sortable-header" data-sort-key="warehouse">Warehouse <span class="sort-indicator"></span></th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider sortable-header" data-sort-key="taxId">Tax ID <span class="sort-indicator"></span></th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider sortable-header" data-sort-key="contactPerson">Contact <span class="sort-indicator"></span></th>
                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-neutral-400 uppercase tracking-wider sortable-header" data-sort-key="isDefault">Default <span class="sort-indicator"></span></th>
                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-neutral-400 uppercase tracking-wider sortable-header" data-sort-key="isActive">Active <span class="sort-indicator"></span></th>
                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-neutral-400 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="consignee-table-body" class="divide-y divide-neutral-700">
                        </tbody>
                </table>
            </div>
            <div id="pagination-controls" class="px-6 py-4 border-t border-neutral-700 flex flex-col sm:flex-row items-center justify-between">
                <div class="flex items-center gap-4 text-sm text-neutral-400 mb-2 sm:mb-0">
                    <span>Showing <span id="current-page-start">1</span>-<span id="current-page-end">0</span> of <span id="total-items">0</span> results</span>
                    <div class="flex items-center">
                        <label for="items-per-page-select" class="mr-2">Show:</label>
                        <select id="items-per-page-select" class="bg-neutral-700 border border-neutral-600 text-neutral-200 text-xs rounded-md focus:ring-neutral-500 focus:border-neutral-500 p-1.5 appearance-none">
                            <option value="5">5</option> 
                            <option value="7" selected>7</option> 
                            <option value="10">10</option> 
                            <option value="15">15</option>
                        </select>
                         <span class="ml-1">per page</span>
                    </div>
                </div>
                <div class="inline-flex rounded-md shadow-sm -space-x-px" role="group">
                    <button id="prev-page-btn" class="relative inline-flex items-center px-3 py-2 rounded-l-md border border-neutral-600 bg-neutral-700 text-sm font-medium text-neutral-300 hover:bg-neutral-600 disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-chevron-left mr-1"></i> Previous
                    </button>
                    <div class="relative inline-flex items-center px-4 py-2 border-y border-neutral-600 bg-neutral-700 text-sm font-medium text-neutral-300">
                        Page <span id="current-page-display">1</span>
                    </div>
                    <button id="next-page-btn" class="relative inline-flex items-center px-3 py-2 rounded-r-md border border-neutral-600 bg-neutral-700 text-sm font-medium text-neutral-300 hover:bg-neutral-600 disabled:opacity-50 disabled:cursor-not-allowed">
                        Next <i class="fas fa-chevron-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div id="consignee-modal" class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center p-4 z-50 hidden modal-enter">
        <div class="bg-neutral-800 p-6 sm:p-8 rounded-xl shadow-2xl w-full max-w-2xl transform transition-all border border-neutral-700 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6 sticky top-0 bg-neutral-800 py-2 z-10">
                <h2 id="modal-title" class="text-2xl font-semibold text-neutral-100">Add New Consignee</h2>
                <button id="close-modal-btn" class="text-neutral-400 hover:text-neutral-200 transition-colors">
                    <i class="fas fa-times fa-lg"></i>
                </button>
            </div>
            <form id="consignee-form" class="space-y-6 pt-2">
                <input type="hidden" id="consignee-id" name="id">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="location" class="block text-sm font-medium text-neutral-300 mb-2">Location Code <span class="text-red-400">*</span></label>
                        <input type="text" id="location" name="location" required class="w-full bg-neutral-700 border border-neutral-600 text-neutral-200 rounded-lg p-2.5 focus:ring-neutral-500 focus:border-neutral-500" placeholder="e.g., 177N-MB01">
                        <button type="button" id="generate-location-code-btn" class="mt-1 text-xs text-neutral-400 hover:text-neutral-300 flex items-center">
                           <i class="fas fa-lightbulb mr-1"></i> ✨ Generate Code
                        </button>
                        <div id="location-code-loader" class="hidden mt-1 text-xs text-neutral-400"><div class="spinner !w-3 !h-3"></div>Generating...</div>
                    </div>
                    <div class="custom-select-wrapper">
                        <label for="type" class="block text-sm font-medium text-neutral-300 mb-2">Consignee Type <span class="text-red-400">*</span></label>
                        <select id="type" name="type" required class="w-full bg-neutral-700 border border-neutral-600 text-neutral-200 rounded-lg p-2.5 focus:ring-neutral-500 focus:border-neutral-500">
                            <option value="">Select Type...</option>
                            <option value="Retailer">Retailer</option>
                            <option value="Distributor">Distributor</option>
                            <option value="End Customer">End Customer</option>
                            <option value="Internal Transfer">Internal Transfer</option>
                            <option value="Warehouse">Warehouse</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="warehouse" class="block text-sm font-medium text-neutral-300 mb-2">Warehouse <span class="text-red-400">*</span></label>
                        <input type="text" id="warehouse" name="warehouse" required class="w-full bg-neutral-700 border border-neutral-600 text-neutral-200 rounded-lg p-2.5 focus:ring-neutral-500 focus:border-neutral-500" placeholder="e.g., Main Warehouse">
                    </div>
                    <div>
                        <label for="taxId" class="block text-sm font-medium text-neutral-300 mb-2">Tax ID (e.g., GSTIN)</label>
                        <input type="text" id="taxId" name="taxId" class="w-full bg-neutral-700 border border-neutral-600 text-neutral-200 rounded-lg p-2.5 focus:ring-neutral-500 focus:border-neutral-500" placeholder="Enter Tax ID">
                    </div>
                </div>
                
                <div>
                    <label for="address" class="block text-sm font-medium text-neutral-300 mb-2">Full Address <span class="text-red-400">*</span></label>
                    <textarea id="address" name="address" rows="3" required class="w-full bg-neutral-700 border border-neutral-600 text-neutral-200 rounded-lg p-2.5 focus:ring-neutral-500 focus:border-neutral-500" placeholder="Enter full street address, city, state, ZIP"></textarea>
                    <button type="button" id="suggest-address-btn" class="mt-2 flex items-center text-sm text-neutral-400 hover:text-neutral-300">
                        <i class="fas fa-lightbulb mr-2"></i> ✨ Suggest Address
                    </button>
                    <div id="address-suggestion-loader" class="hidden mt-2 text-sm text-neutral-400"><div class="spinner"></div>Getting suggestion...</div>
                </div>

                <fieldset class="border border-neutral-600 p-4 rounded-lg">
                    <legend class="text-sm font-medium text-neutral-300 px-2">Contact Information</legend>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-2">
                        <div>
                            <label for="contactPerson" class="block text-xs font-medium text-neutral-400 mb-1">Contact Person</label>
                            <input type="text" id="contactPerson" name="contactPerson" class="w-full bg-neutral-700 border border-neutral-600 text-neutral-200 rounded-lg p-2.5 focus:ring-neutral-500 focus:border-neutral-500 text-sm" placeholder="Full Name">
                        </div>
                        <div>
                            <label for="contactPhone" class="block text-xs font-medium text-neutral-400 mb-1">Contact Phone</label>
                            <input type="tel" id="contactPhone" name="contactPhone" class="w-full bg-neutral-700 border border-neutral-600 text-neutral-200 rounded-lg p-2.5 focus:ring-neutral-500 focus:border-neutral-500 text-sm" placeholder="Phone Number">
                        </div>
                        <div>
                            <label for="contactEmail" class="block text-xs font-medium text-neutral-400 mb-1">Contact Email</label>
                            <input type="email" id="contactEmail" name="contactEmail" class="w-full bg-neutral-700 border border-neutral-600 text-neutral-200 rounded-lg p-2.5 focus:ring-neutral-500 focus:border-neutral-500 text-sm" placeholder="Email Address">
                        </div>
                    </div>
                </fieldset>
                
                <div>
                    <label for="specialInstructions" class="block text-sm font-medium text-neutral-300 mb-2">Special Instructions</label>
                    <textarea id="specialInstructions" name="specialInstructions" rows="2" class="w-full bg-neutral-700 border border-neutral-600 text-neutral-200 rounded-lg p-2.5 focus:ring-neutral-500 focus:border-neutral-500" placeholder="e.g., Delivery notes, handling preferences"></textarea>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 pt-2">
                    <div class="flex items-center">
                        <input id="isDefault" name="isDefault" type="checkbox" class="custom-checkbox mr-3">
                        <label for="isDefault" class="text-sm font-medium text-neutral-300">Set as Default Consignee</label>
                    </div>
                    <div class="flex items-center">
                        <input id="isActive" name="isActive" type="checkbox" class="custom-checkbox mr-3" checked>
                        <label for="isActive" class="text-sm font-medium text-neutral-300">Is Active</label>
                    </div>
                </div>

                <div class="flex justify-end gap-4 pt-4 sticky bottom-0 bg-neutral-800 py-4 z-10">
                    <button type="button" id="cancel-modal-btn" class="py-2 px-5 bg-neutral-600 hover:bg-neutral-500 text-neutral-200 font-semibold rounded-lg shadow-md">Cancel</button>
                    <button type="submit" id="save-consignee-btn" class="py-2 px-5 bg-neutral-200 hover:bg-neutral-300 text-neutral-800 font-semibold rounded-lg shadow-md transform hover:scale-105">Save Consignee</button>
                </div>
            </form>
        </div>
    </div>
    
    <div id="notification-modal" class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center p-4 z-[60] hidden modal-enter">
        <div class="bg-neutral-800 p-6 rounded-xl shadow-2xl w-full max-w-md transform transition-all border border-neutral-700">
            <div class="flex items-start mb-4">
                <div id="notification-icon-container" class="mr-3 mt-1">
                    </div>
                <div class="flex-1">
                    <h3 id="notification-title" class="text-xl font-semibold text-neutral-100">Notification</h3>
                    <p id="notification-message" class="text-neutral-300 mt-1">Message here.</p>
                </div>
            </div>
            <div id="notification-loader" class="hidden mb-6">
                <div class="flex items-center justify-center text-neutral-400">
                    <div class="spinner"></div>
                    <span class="ml-2">Processing...</span>
                </div>
            </div>
            <div id="notification-buttons" class="flex justify-end gap-3">
                <button id="notification-cancel-btn" class="py-2 px-4 bg-neutral-600 hover:bg-neutral-500 text-neutral-200 font-medium rounded-lg transition-colors">Cancel</button>
                <button id="notification-confirm-btn" class="py-2 px-4 bg-red-700 hover:bg-red-600 text-white font-medium rounded-lg transition-colors">Confirm</button> 
            </div>
        </div>
    </div>

    <div id="toast-container"></div>

    <script src="index.js"></script>
</body>
</html>
